'use client'

import React, { useState, useRef, useEffect, use<PERSON><PERSON>back } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Send,
  X,
  Copy,
  Check,
  Code,
  Database,
  BarChart3,
  TrendingUp,
  Award,
  Shield,
  Wand2,
  Sparkles,
  Loader2,
  ChevronRight,
  ChevronDown,
  Plus,
  FileText,
  Brain,
  Zap,
  MessageSquare,
  History,
  Settings,
  ChevronLeft,
  PanelRightClose,
  PanelRightO<PERSON>,
  Target,
  ArrowDown
} from "lucide-react"
import { toast } from "sonner"
import { Dataset } from '@/types/index'
import { CellData } from './chartbuilderlogic'
import { cn } from "@/lib/utils"

interface DataCopilotProps {
  cells: CellData[]
  datasets: Dataset[]
  selectedDatasets: Dataset[]
  onInsertCode: (code: string, cellId?: string, createNew?: boolean) => void
  onCreateNewCell: (code: string, language: string) => void
  isCollapsed: boolean
  onToggleCollapse: () => void
}

interface ConversationMessage {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
  code?: string
  language?: string
  cellContext?: string
}

interface CellSummary {
  id: string
  index: number
  language: string
  hasResult: boolean
  hasError: boolean
  summary: string
  datasetCount: number
}

export function DataCopilot({
  cells,
  datasets,
  selectedDatasets,
  onInsertCode,
  onCreateNewCell,
  isCollapsed,
  onToggleCollapse
}: DataCopilotProps) {
  // Add shimmer animation styles
  useEffect(() => {
    const style = document.createElement('style')
    style.textContent = `
      @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
      }
      .animate-shimmer {
        animation: shimmer 2s infinite;
      }
    `
    document.head.appendChild(style)
    return () => {
      if (document.head.contains(style)) {
        document.head.removeChild(style)
      }
    }
  }, [])
  const [conversation, setConversation] = useState<ConversationMessage[]>([])
  const [prompt, setPrompt] = useState('')
  const [isStreaming, setIsStreaming] = useState(false)
  const [streamedResponse, setStreamedResponse] = useState('')
  const [showCellContext, setShowCellContext] = useState(true)
  const [showDatasetOverview, setShowDatasetOverview] = useState(true)
  const [selectedCellForInsertion, setSelectedCellForInsertion] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'chat' | 'context' | 'history'>('chat')
  
  const inputRef = useRef<HTMLInputElement>(null)
  const abortControllerRef = useRef<AbortController | null>(null)
  const conversationEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom of conversation
  useEffect(() => {
    conversationEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [conversation, streamedResponse])

  // Focus input when not collapsed
  useEffect(() => {
    if (!isCollapsed && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isCollapsed])

  // Analyze cell patterns and relationships
  const analyzeCellPatterns = useCallback(() => {
    const patterns = {
      dataLoading: cells.filter(cell =>
        cell.content.includes('SELECT') ||
        cell.content.includes('pd.read') ||
        cell.content.includes('FROM')
      ).length,
      visualization: cells.filter(cell =>
        cell.content.includes('plt.') ||
        cell.content.includes('sns.') ||
        cell.content.includes('plotly') ||
        cell.content.includes('chart')
      ).length,
      dataProcessing: cells.filter(cell =>
        cell.content.includes('groupby') ||
        cell.content.includes('merge') ||
        cell.content.includes('join') ||
        cell.content.includes('GROUP BY')
      ).length,
      statisticalAnalysis: cells.filter(cell =>
        cell.content.includes('describe') ||
        cell.content.includes('corr') ||
        cell.content.includes('mean') ||
        cell.content.includes('std')
      ).length
    }

    const workflow = {
      hasDataLoading: patterns.dataLoading > 0,
      hasVisualization: patterns.visualization > 0,
      hasProcessing: patterns.dataProcessing > 0,
      hasAnalysis: patterns.statisticalAnalysis > 0,
      suggestedNextSteps: [] as string[]
    }

    // Suggest next steps based on current workflow
    if (!workflow.hasDataLoading && datasets.length > 0) {
      workflow.suggestedNextSteps.push('Load and explore your datasets')
    }
    if (workflow.hasDataLoading && !workflow.hasAnalysis) {
      workflow.suggestedNextSteps.push('Perform statistical analysis')
    }
    if (workflow.hasAnalysis && !workflow.hasVisualization) {
      workflow.suggestedNextSteps.push('Create visualizations')
    }

    return { patterns, workflow }
  }, [cells, datasets])

  // Helper function to categorize cell content
  const getCellCategory = (content: string, language: string) => {
    if (language === 'markdown') return 'documentation'

    const lowerContent = content.toLowerCase()
    if (lowerContent.includes('select') || lowerContent.includes('from')) return 'data_query'
    if (lowerContent.includes('plt.') || lowerContent.includes('sns.') || lowerContent.includes('plotly')) return 'visualization'
    if (lowerContent.includes('groupby') || lowerContent.includes('merge') || lowerContent.includes('join')) return 'data_processing'
    if (lowerContent.includes('describe') || lowerContent.includes('corr') || lowerContent.includes('mean')) return 'analysis'
    if (lowerContent.includes('pd.read') || lowerContent.includes('import')) return 'data_loading'

    return 'general'
  }

  // Helper function to extract operations from cell content
  const extractOperations = (content: string, language: string) => {
    const operations = []
    const lowerContent = content.toLowerCase()

    if (language === 'sql') {
      if (lowerContent.includes('select')) operations.push('SELECT')
      if (lowerContent.includes('where')) operations.push('WHERE')
      if (lowerContent.includes('group by')) operations.push('GROUP BY')
      if (lowerContent.includes('order by')) operations.push('ORDER BY')
      if (lowerContent.includes('join')) operations.push('JOIN')
    } else if (language === 'python') {
      if (lowerContent.includes('groupby')) operations.push('groupby')
      if (lowerContent.includes('merge')) operations.push('merge')
      if (lowerContent.includes('plot')) operations.push('plotting')
      if (lowerContent.includes('describe')) operations.push('describe')
      if (lowerContent.includes('corr')) operations.push('correlation')
    }

    return operations
  }

  // Helper function to determine current workflow stage
  const getCurrentWorkflowStage = (workflow: any) => {
    if (!workflow.hasDataLoading) return 'setup'
    if (!workflow.hasProcessing && !workflow.hasAnalysis) return 'exploration'
    if (!workflow.hasVisualization) return 'analysis'
    return 'presentation'
  }

  // Generate comprehensive context for AI
  const generateContext = useCallback(() => {
    const { patterns, workflow } = analyzeCellPatterns()

    const cellsContext = cells.map((cell, index) => {
      // Analyze cell content for better context
      const cellAnalysis = {
        cellNumber: index + 1,
        language: cell.language,
        content: cell.content,
        contentLength: cell.content.length,
        hasResult: !!cell.result?.data?.length,
        hasError: !!cell.error,
        errorMessage: cell.error || undefined,
        resultRowCount: cell.result?.data?.length || 0,
        selectedDatasets: cell.selectedDatasetIds?.map(id =>
          datasets.find(ds => ds.id === id)?.name
        ).filter(Boolean) || [],
        // Categorize cell type
        cellCategory: getCellCategory(cell.content, cell.language),
        // Extract key operations
        operations: extractOperations(cell.content, cell.language)
      }
      return cellAnalysis
    })

    const datasetsContext = datasets.map(ds => {
      // Enhanced dataset analysis
      const columnTypes = ds.headers?.map(header => {
        const sampleValues = ds.data?.slice(0, 10).map(row => row[header]).filter(val => val != null) || []
        const isNumeric = sampleValues.every(val => !isNaN(Number(val)) && val !== '')
        const isDate = sampleValues.some(val => !isNaN(Date.parse(val)))
        const uniqueValues = new Set(sampleValues).size

        return {
          name: header,
          type: isNumeric ? 'numeric' : isDate ? 'date' : 'text',
          uniqueValues: uniqueValues,
          sampleValues: sampleValues.slice(0, 3),
          hasNulls: sampleValues.length < 10
        }
      }) || []

      return {
        name: ds.name,
        columns: ds.headers,
        columnTypes: columnTypes,
        rowCount: ds.data?.length || 0,
        sampleData: ds.data?.slice(0, 3) || [],
        fileType: ds.fileType,
        // Dataset quality metrics
        qualityMetrics: {
          totalColumns: ds.headers?.length || 0,
          numericColumns: columnTypes.filter(col => col.type === 'numeric').length,
          textColumns: columnTypes.filter(col => col.type === 'text').length,
          dateColumns: columnTypes.filter(col => col.type === 'date').length
        }
      }
    })

    return {
      cells: cellsContext,
      datasets: datasetsContext,
      totalCells: cells.length,
      totalDatasets: datasets.length,
      selectedDatasets: selectedDatasets.map(ds => ds.name),
      workflowAnalysis: {
        patterns,
        workflow,
        currentStage: getCurrentWorkflowStage(workflow),
        suggestedNextSteps: workflow.suggestedNextSteps
      }
    }
  }, [cells, datasets, selectedDatasets, analyzeCellPatterns])

  const handleGenerate = async (quickPrompt?: string) => {
    const currentPrompt = quickPrompt || prompt.trim()

    if (!currentPrompt) {
      toast.error('Please enter a prompt')
      return
    }

    // Add user message to conversation
    const userMessage: ConversationMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: currentPrompt,
      timestamp: new Date()
    }
    setConversation(prev => [...prev, userMessage])

    // Abort any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    abortControllerRef.current = new AbortController()
    setIsStreaming(true)
    setStreamedResponse('')

    if (!quickPrompt) {
      setPrompt('') // Clear input only if not using quick action
    }

    try {
      const context = generateContext()

      const response = await fetch('/api/ai/copilot/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: currentPrompt,
          context: context,
          conversationHistory: conversation.slice(-5) // Last 5 messages for context
        }),
        signal: abortControllerRef.current.signal
      })

      if (!response.ok) {
        throw new Error('Failed to generate response')
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('No response body')
      }

      let accumulatedResponse = ''
      let accumulatedCode = ''
      let detectedLanguage = 'sql'
      const decoder = new TextDecoder()

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              if (data.content) {
                accumulatedResponse += data.content
                setStreamedResponse(accumulatedResponse)
              }
              if (data.code) {
                accumulatedCode += data.code
              }
              if (data.language) {
                detectedLanguage = data.language
              }
              if (data.done) {
                // Add assistant message to conversation
                const assistantMessage: ConversationMessage = {
                  id: Date.now().toString(),
                  type: 'assistant',
                  content: accumulatedResponse,
                  timestamp: new Date(),
                  code: accumulatedCode || undefined,
                  language: detectedLanguage
                }
                setConversation(prev => [...prev, assistantMessage])
                setStreamedResponse('')

                if (accumulatedCode) {
                  toast.success('Code generated! You can insert it into a cell.')
                }
                return
              }
            } catch (e) {
              // Ignore parsing errors for incomplete chunks
            }
          }
        }
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        toast.info('Generation cancelled')
      } else {
        console.error('Error generating response:', error)
        toast.error('Failed to generate response. Please try again.')
      }
    } finally {
      setIsStreaming(false)
      abortControllerRef.current = null
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleGenerate()
    }
    if (e.key === 'Escape') {
      setPrompt('')
    }
  }

  const handleStop = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    setIsStreaming(false)
    setStreamedResponse('')
    toast.info('Generation stopped')
  }

  const handleInsertCode = (code: string, language: string) => {
    if (selectedCellForInsertion) {
      onInsertCode(code, selectedCellForInsertion)
      toast.success('Code inserted into selected cell!')
    } else {
      onCreateNewCell(code, language)
      toast.success('New cell created with generated code!')
    }
  }

  // Generate cell summaries for context
  const cellSummaries: CellSummary[] = cells.map((cell, index) => ({
    id: cell.id,
    index: index + 1,
    language: cell.language,
    hasResult: !!cell.result?.data?.length,
    hasError: !!cell.error,
    summary: cell.content.slice(0, 100) + (cell.content.length > 100 ? '...' : ''),
    datasetCount: cell.selectedDatasetIds?.length || 0
  }))

  // Quick action prompts
  const quickActions = [
    { 
      label: "Analyze Data", 
      prompt: "Analyze the current datasets and provide insights", 
      icon: BarChart3,
      description: "Get statistical insights from your data"
    },
    { 
      label: "Create Chart", 
      prompt: "Create a visualization showing the main trends in the data", 
      icon: TrendingUp,
      description: "Generate code for data visualization"
    },
    { 
      label: "Data Quality", 
      prompt: "Check for missing values, duplicates, and data quality issues", 
      icon: Shield,
      description: "Validate and clean your data"
    },
    { 
      label: "Join Tables", 
      prompt: "Help me join multiple datasets together", 
      icon: Database,
      description: "Combine data from multiple sources"
    }
  ]

  if (isCollapsed) {
    return (
      <div className="w-12 h-full bg-gradient-to-b from-background to-muted/20 border-l border-border/50 flex flex-col items-center py-4 shadow-sm">
        <Button
          onClick={onToggleCollapse}
          variant="ghost"
          size="sm"
          className="w-10 h-10 p-0 mb-4 hover:bg-violet-100 hover:scale-105 transition-all duration-200 rounded-xl"
          title="Open Data Copilot"
        >
          <PanelRightOpen className="h-4 w-4 text-violet-600" />
        </Button>
        <div className="flex flex-col gap-3 items-center">
          <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-violet-500 via-blue-500 to-purple-600 flex items-center justify-center shadow-lg">
            <Brain className="h-4 w-4 text-white" />
          </div>
          {conversation.length > 0 && (
            <Badge variant="secondary" className="w-6 h-6 rounded-full p-0 flex items-center justify-center text-xs bg-violet-100 text-violet-700 border-violet-200">
              {conversation.length}
            </Badge>
          )}
          {isStreaming && (
            <div className="w-2 h-2 rounded-full bg-violet-500 animate-pulse"></div>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="w-96 max-w-[400px] min-w-[350px] h-full bg-gradient-to-b from-background via-background to-muted/10 border-l border-border/50 flex flex-col shadow-xl">
      {/* Enhanced Header */}
      <div className="p-4 border-b border-border/50 bg-gradient-to-r from-violet-50/50 to-blue-50/50">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-violet-500 via-blue-500 to-purple-600 flex items-center justify-center shadow-lg">
              <Brain className="h-5 w-5 text-white" />
            </div>
            <div>
              <h3 className="font-bold text-sm bg-gradient-to-r from-violet-600 to-blue-600 bg-clip-text text-transparent">
                Data Copilot
              </h3>
              <p className="text-xs text-muted-foreground">AI-powered data assistant</p>
            </div>
          </div>
          <Button
            onClick={onToggleCollapse}
            variant="ghost"
            size="sm"
            className="w-8 h-8 p-0 hover:bg-violet-100 hover:scale-105 transition-all duration-200 rounded-lg"
            title="Close Data Copilot"
          >
            <PanelRightClose className="h-4 w-4 text-violet-600" />
          </Button>
        </div>

        {/* Enhanced Context Summary */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-xs">
              <div className="flex items-center gap-1 px-2 py-1 rounded-md bg-blue-50 text-blue-700">
                <Database className="h-3 w-3" />
                <span className="font-medium">{datasets.length}</span>
                <span>datasets</span>
              </div>
              <div className="flex items-center gap-1 px-2 py-1 rounded-md bg-green-50 text-green-700">
                <FileText className="h-3 w-3" />
                <span className="font-medium">{cells.length}</span>
                <span>cells</span>
              </div>
            </div>
            {isStreaming && (
              <div className="flex items-center gap-1 text-xs text-violet-600">
                <div className="w-2 h-2 rounded-full bg-violet-500 animate-pulse"></div>
                <span>Thinking...</span>
              </div>
            )}
          </div>
          {selectedDatasets.length > 0 && (
            <Badge variant="outline" className="text-xs bg-violet-50 text-violet-700 border-violet-200">
              {selectedDatasets.length} selected dataset{selectedDatasets.length !== 1 ? 's' : ''}
            </Badge>
          )}
        </div>
      </div>

      {/* Tabbed Interface */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-3 mx-4 mt-2">
          <TabsTrigger value="chat" className="text-xs">
            <MessageSquare className="h-3 w-3 mr-1" />
            Chat
          </TabsTrigger>
          <TabsTrigger value="context" className="text-xs">
            <FileText className="h-3 w-3 mr-1" />
            Context
          </TabsTrigger>
          <TabsTrigger value="history" className="text-xs">
            <History className="h-3 w-3 mr-1" />
            History
          </TabsTrigger>
        </TabsList>

        <TabsContent value="chat" className="flex-1 flex flex-col mt-0">
          {/* Enhanced Quick Actions */}
          <div className="p-4 border-b border-border/50 bg-gradient-to-r from-violet-50/30 to-blue-50/30">
            <h4 className="text-xs font-semibold text-gray-700 mb-3 flex items-center gap-2">
              <Zap className="h-3 w-3 text-violet-600" />
              Quick Actions
            </h4>
            <div className="grid grid-cols-2 gap-2">
              {quickActions.map((action, index) => {
                const IconComponent = action.icon
                return (
                  <Button
                    key={index}
                    onClick={() => handleGenerate(action.prompt)}
                    disabled={isStreaming}
                    variant="outline"
                    size="sm"
                    className={`h-auto p-3 flex flex-col items-center gap-2 text-xs transition-all duration-200 hover:scale-105 hover:shadow-md ${
                      isStreaming
                        ? 'opacity-50 cursor-not-allowed'
                        : 'hover:bg-gradient-to-br hover:from-violet-50 hover:to-blue-50 hover:border-violet-300'
                    }`}
                    title={action.description}
                  >
                    <div className="w-6 h-6 rounded-lg bg-gradient-to-br from-violet-100 to-blue-100 flex items-center justify-center">
                      <IconComponent className="h-3 w-3 text-violet-600" />
                    </div>
                    <span className="text-xs font-medium">{action.label}</span>
                  </Button>
                )
              })}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="context" className="flex-1 flex flex-col mt-0">
          {/* Context Overview */}
          <div className="p-4">
            {/* Workflow Stage */}
            {(() => {
              const { workflow } = analyzeCellPatterns()
              const stage = getCurrentWorkflowStage(workflow)
              return (
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-xs">
                    <Target className="h-3 w-3" />
                    <span>Stage: {stage}</span>
                    <Badge variant="outline" className="text-xs">
                      {cells.filter(c => c.result?.data?.length).length}/{cells.length} executed
                    </Badge>
                  </div>

                  {/* Dataset Overview */}
                  <div className="space-y-2">
                    <h5 className="text-xs font-medium">Datasets ({datasets.length})</h5>
                    {datasets.slice(0, 3).map((dataset) => (
                      <div key={dataset.id} className="text-xs p-2 rounded bg-muted/50">
                        <div className="font-medium">{dataset.name}</div>
                        <div className="text-muted-foreground">
                          {dataset.headers?.length || 0} columns, {dataset.data?.length || 0} rows
                        </div>
                      </div>
                    ))}
                    {datasets.length > 3 && (
                      <div className="text-xs text-muted-foreground text-center">
                        +{datasets.length - 3} more datasets
                      </div>
                    )}
                  </div>

                  {/* Cell Summary */}
                  <div className="space-y-2">
                    <h5 className="text-xs font-medium">Cells ({cells.length})</h5>
                    {cellSummaries.slice(0, 4).map((cell) => (
                      <div key={cell.id} className="flex items-center gap-2 text-xs p-1 rounded bg-muted/50">
                        <Badge variant="outline" className="text-xs">
                          {cell.language}
                        </Badge>
                        <span className="flex-1 truncate">Cell {cell.index}</span>
                        {cell.hasResult && <Check className="h-3 w-3 text-green-500" />}
                        {cell.hasError && <X className="h-3 w-3 text-red-500" />}
                      </div>
                    ))}
                    {cellSummaries.length > 4 && (
                      <div className="text-xs text-muted-foreground text-center">
                        +{cellSummaries.length - 4} more cells
                      </div>
                    )}
                  </div>
                </div>
              )
            })()}
          </div>
        </TabsContent>

        <TabsContent value="history" className="flex-1 flex flex-col mt-0">
          {/* Conversation History */}
          <div className="p-4">
            <h4 className="text-xs font-medium text-muted-foreground mb-2">Recent Conversations</h4>
            {conversation.length === 0 ? (
              <div className="text-xs text-muted-foreground text-center py-4">
                No conversation history yet
              </div>
            ) : (
              <div className="space-y-2">
                {conversation.slice(-5).map((message) => (
                  <div key={message.id} className="text-xs p-2 rounded bg-muted/50">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant={message.type === 'user' ? 'default' : 'secondary'} className="text-xs">
                        {message.type}
                      </Badge>
                      <span className="text-muted-foreground">
                        {message.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                    <div className="truncate">{message.content}</div>
                    {message.code && (
                      <Badge variant="outline" className="text-xs mt-1">
                        {message.language} code
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
          {/* Conversation Area */}
          <div className="flex-1 flex flex-col">
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-4">
                {conversation.length === 0 && (
                  <div className="text-center py-8">
                    <Brain className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                    <p className="text-sm text-muted-foreground">
                      Ask me anything about your data!
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      I can analyze, visualize, and help you write code.
                    </p>
                  </div>
                )}
            
            {conversation.map((message) => (
              <div key={message.id} className={cn(
                "flex gap-2",
                message.type === 'user' ? 'justify-end' : 'justify-start'
              )}>
                <div className={cn(
                  "max-w-[90%] rounded-xl p-4 text-sm shadow-sm",
                  message.type === 'user'
                    ? 'bg-gradient-to-br from-violet-500 to-blue-500 text-white'
                    : 'bg-gradient-to-br from-gray-50 to-white border border-gray-200'
                )}>
                  <p className="whitespace-pre-wrap leading-relaxed">{message.content}</p>
                  {message.code && (
                    <div className="mt-4 space-y-3">
                      <div className="p-3 bg-gray-900 rounded-lg border text-xs font-mono overflow-hidden">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <Badge variant="secondary" className="text-xs bg-violet-100 text-violet-700 border-violet-200">
                              {message.language || 'code'}
                            </Badge>
                            <div className="flex gap-1">
                              <div className="w-2 h-2 rounded-full bg-red-400"></div>
                              <div className="w-2 h-2 rounded-full bg-yellow-400"></div>
                              <div className="w-2 h-2 rounded-full bg-green-400"></div>
                            </div>
                          </div>
                          <Button
                            onClick={() => navigator.clipboard.writeText(message.code!)}
                            size="sm"
                            variant="ghost"
                            className="h-6 w-6 p-0 hover:bg-gray-700 text-gray-400 hover:text-white"
                            title="Copy code"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                        <div className="overflow-x-auto">
                          <pre className="text-green-400 text-xs leading-relaxed whitespace-pre-wrap break-words">{message.code}</pre>
                        </div>
                      </div>

                      {/* Enhanced Code insertion options */}
                      <div className="space-y-3 bg-gray-50 p-3 rounded-lg border">
                        <div className="flex gap-2 flex-wrap">
                          <Button
                            onClick={() => handleInsertCode(message.code!, message.language || 'sql')}
                            size="sm"
                            variant="outline"
                            className="h-8 px-3 text-xs bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 text-green-700 hover:bg-green-100 hover:border-green-300 transition-all duration-200"
                          >
                            <Plus className="h-3 w-3 mr-1" />
                            New Cell
                          </Button>

                          <Button
                            onClick={() => navigator.clipboard.writeText(message.code!)}
                            size="sm"
                            variant="ghost"
                            className="h-8 px-3 text-xs text-gray-600 hover:bg-gray-100 transition-all duration-200"
                          >
                            <Copy className="h-3 w-3 mr-1" />
                            Copy
                          </Button>
                        </div>

                        {cells.length > 0 && (
                          <div className="flex items-center gap-2 pt-2 border-t border-gray-200">
                            <span className="text-xs font-medium text-gray-600">Insert into:</span>
                            <Select onValueChange={(cellId) => onInsertCode(message.code!, cellId)}>
                              <SelectTrigger className="h-8 w-36 text-xs border-violet-200 focus:border-violet-400 focus:ring-violet-400/20">
                                <SelectValue placeholder="Select cell" />
                              </SelectTrigger>
                              <SelectContent className="max-w-64">
                                {cells.map((cell, index) => (
                                  <SelectItem key={cell.id} value={cell.id} className="cursor-pointer">
                                    <div className="flex items-center gap-2 w-full">
                                      <Badge variant="outline" className="text-xs bg-violet-50 text-violet-700 border-violet-200">
                                        {cell.language}
                                      </Badge>
                                      <span className="font-medium">Cell {index + 1}</span>
                                      {cell.result?.data?.length && (
                                        <span className="text-xs text-green-600 bg-green-50 px-1 rounded">
                                          {cell.result.data.length} rows
                                        </span>
                                      )}
                                      {cell.error && (
                                        <span className="text-xs text-red-600">⚠️</span>
                                      )}
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Timestamp */}
                  <div className="mt-2 text-xs opacity-60">
                    {message.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              </div>
            ))}
            
            {isStreaming && streamedResponse && (
              <div className="flex gap-2 justify-start">
                <div className="max-w-[85%] rounded-xl p-4 text-sm bg-gradient-to-br from-violet-50 to-blue-50 border border-violet-200/50 shadow-sm">
                  <div className="flex items-center gap-2 mb-3">
                    <div className="w-4 h-4 border-2 border-violet-200 border-t-violet-500 rounded-full animate-spin"></div>
                    <span className="text-xs font-medium bg-gradient-to-r from-violet-600 to-blue-600 bg-clip-text text-transparent">
                      AI is thinking...
                    </span>
                    <div className="flex gap-1">
                      <div className="w-1 h-1 rounded-full bg-violet-400 animate-pulse"></div>
                      <div className="w-1 h-1 rounded-full bg-violet-400 animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                      <div className="w-1 h-1 rounded-full bg-violet-400 animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                    </div>
                  </div>
                  <div className="relative">
                    <p className="whitespace-pre-wrap text-gray-700 leading-relaxed">{streamedResponse}</p>
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer pointer-events-none"></div>
                  </div>
                </div>
              </div>
            )}

            <div ref={conversationEndRef} />
          </div>
        </ScrollArea>

        {/* Enhanced Input Area */}
        <div className="p-4 border-t border-border/50 bg-gradient-to-r from-violet-50/30 to-blue-50/30">
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Input
                ref={inputRef}
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder="Ask about your data..."
                className={`text-sm pr-10 border-violet-200 focus:border-violet-400 focus:ring-violet-400/20 transition-all duration-200 ${
                  isStreaming ? 'bg-muted/50 cursor-not-allowed' : 'bg-white hover:border-violet-300'
                }`}
                disabled={isStreaming}
              />
              {isStreaming && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <div className="w-4 h-4 border-2 border-violet-200 border-t-violet-500 rounded-full animate-spin"></div>
                </div>
              )}
            </div>
            {isStreaming ? (
              <Button
                onClick={handleStop}
                size="sm"
                variant="outline"
                className="px-3 border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300 transition-all duration-200"
              >
                <X className="h-4 w-4" />
              </Button>
            ) : (
              <Button
                onClick={() => handleGenerate()}
                disabled={!prompt.trim()}
                size="sm"
                className="px-3 bg-gradient-to-r from-violet-500 to-blue-500 hover:from-violet-600 hover:to-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <Send className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
