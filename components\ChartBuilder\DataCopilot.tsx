'use client'

import React, { useState, useRef, useEffect, use<PERSON><PERSON>back } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Send,
  X,
  Copy,
  Check,
  Code,
  Database,
  BarChart3,
  TrendingUp,
  Award,
  Shield,
  Wand2,
  Sparkles,
  Loader2,
  ChevronRight,
  ChevronDown,
  Plus,
  FileText,
  Brain,
  Zap,
  MessageSquare,
  History,
  Settings,
  ChevronLeft,
  PanelRightClose,
  PanelRightO<PERSON>,
  Target,
  ArrowDown
} from "lucide-react"
import { toast } from "sonner"
import { Dataset } from '@/types/index'
import { CellData } from './chartbuilderlogic'
import { cn } from "@/lib/utils"

interface DataCopilotProps {
  cells: CellData[]
  datasets: Dataset[]
  selectedDatasets: Dataset[]
  onInsertCode: (code: string, cellId?: string, createNew?: boolean) => void
  onCreateNewCell: (code: string, language: string) => void
  isCollapsed: boolean
  onToggleCollapse: () => void
}

interface ConversationMessage {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
  code?: string
  language?: string
  cellContext?: string
}

interface CellSummary {
  id: string
  index: number
  language: string
  hasResult: boolean
  hasError: boolean
  summary: string
  datasetCount: number
}

export function DataCopilot({
  cells,
  datasets,
  selectedDatasets,
  onInsertCode,
  onCreateNewCell,
  isCollapsed,
  onToggleCollapse
}: DataCopilotProps) {
  const [conversation, setConversation] = useState<ConversationMessage[]>([])
  const [prompt, setPrompt] = useState('')
  const [isStreaming, setIsStreaming] = useState(false)
  const [streamedResponse, setStreamedResponse] = useState('')
  const [showCellContext, setShowCellContext] = useState(true)
  const [showDatasetOverview, setShowDatasetOverview] = useState(true)
  const [selectedCellForInsertion, setSelectedCellForInsertion] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'chat' | 'context' | 'history'>('chat')
  
  const inputRef = useRef<HTMLInputElement>(null)
  const abortControllerRef = useRef<AbortController | null>(null)
  const conversationEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom of conversation
  useEffect(() => {
    conversationEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [conversation, streamedResponse])

  // Focus input when not collapsed
  useEffect(() => {
    if (!isCollapsed && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isCollapsed])

  // Analyze cell patterns and relationships
  const analyzeCellPatterns = useCallback(() => {
    const patterns = {
      dataLoading: cells.filter(cell =>
        cell.content.includes('SELECT') ||
        cell.content.includes('pd.read') ||
        cell.content.includes('FROM')
      ).length,
      visualization: cells.filter(cell =>
        cell.content.includes('plt.') ||
        cell.content.includes('sns.') ||
        cell.content.includes('plotly') ||
        cell.content.includes('chart')
      ).length,
      dataProcessing: cells.filter(cell =>
        cell.content.includes('groupby') ||
        cell.content.includes('merge') ||
        cell.content.includes('join') ||
        cell.content.includes('GROUP BY')
      ).length,
      statisticalAnalysis: cells.filter(cell =>
        cell.content.includes('describe') ||
        cell.content.includes('corr') ||
        cell.content.includes('mean') ||
        cell.content.includes('std')
      ).length
    }

    const workflow = {
      hasDataLoading: patterns.dataLoading > 0,
      hasVisualization: patterns.visualization > 0,
      hasProcessing: patterns.dataProcessing > 0,
      hasAnalysis: patterns.statisticalAnalysis > 0,
      suggestedNextSteps: [] as string[]
    }

    // Suggest next steps based on current workflow
    if (!workflow.hasDataLoading && datasets.length > 0) {
      workflow.suggestedNextSteps.push('Load and explore your datasets')
    }
    if (workflow.hasDataLoading && !workflow.hasAnalysis) {
      workflow.suggestedNextSteps.push('Perform statistical analysis')
    }
    if (workflow.hasAnalysis && !workflow.hasVisualization) {
      workflow.suggestedNextSteps.push('Create visualizations')
    }

    return { patterns, workflow }
  }, [cells, datasets])

  // Helper function to categorize cell content
  const getCellCategory = (content: string, language: string) => {
    if (language === 'markdown') return 'documentation'

    const lowerContent = content.toLowerCase()
    if (lowerContent.includes('select') || lowerContent.includes('from')) return 'data_query'
    if (lowerContent.includes('plt.') || lowerContent.includes('sns.') || lowerContent.includes('plotly')) return 'visualization'
    if (lowerContent.includes('groupby') || lowerContent.includes('merge') || lowerContent.includes('join')) return 'data_processing'
    if (lowerContent.includes('describe') || lowerContent.includes('corr') || lowerContent.includes('mean')) return 'analysis'
    if (lowerContent.includes('pd.read') || lowerContent.includes('import')) return 'data_loading'

    return 'general'
  }

  // Helper function to extract operations from cell content
  const extractOperations = (content: string, language: string) => {
    const operations = []
    const lowerContent = content.toLowerCase()

    if (language === 'sql') {
      if (lowerContent.includes('select')) operations.push('SELECT')
      if (lowerContent.includes('where')) operations.push('WHERE')
      if (lowerContent.includes('group by')) operations.push('GROUP BY')
      if (lowerContent.includes('order by')) operations.push('ORDER BY')
      if (lowerContent.includes('join')) operations.push('JOIN')
    } else if (language === 'python') {
      if (lowerContent.includes('groupby')) operations.push('groupby')
      if (lowerContent.includes('merge')) operations.push('merge')
      if (lowerContent.includes('plot')) operations.push('plotting')
      if (lowerContent.includes('describe')) operations.push('describe')
      if (lowerContent.includes('corr')) operations.push('correlation')
    }

    return operations
  }

  // Helper function to determine current workflow stage
  const getCurrentWorkflowStage = (workflow: any) => {
    if (!workflow.hasDataLoading) return 'setup'
    if (!workflow.hasProcessing && !workflow.hasAnalysis) return 'exploration'
    if (!workflow.hasVisualization) return 'analysis'
    return 'presentation'
  }

  // Generate comprehensive context for AI
  const generateContext = useCallback(() => {
    const { patterns, workflow } = analyzeCellPatterns()

    const cellsContext = cells.map((cell, index) => {
      // Analyze cell content for better context
      const cellAnalysis = {
        cellNumber: index + 1,
        language: cell.language,
        content: cell.content,
        contentLength: cell.content.length,
        hasResult: !!cell.result?.data?.length,
        hasError: !!cell.error,
        errorMessage: cell.error || undefined,
        resultRowCount: cell.result?.data?.length || 0,
        selectedDatasets: cell.selectedDatasetIds?.map(id =>
          datasets.find(ds => ds.id === id)?.name
        ).filter(Boolean) || [],
        // Categorize cell type
        cellCategory: getCellCategory(cell.content, cell.language),
        // Extract key operations
        operations: extractOperations(cell.content, cell.language)
      }
      return cellAnalysis
    })

    const datasetsContext = datasets.map(ds => {
      // Enhanced dataset analysis
      const columnTypes = ds.headers?.map(header => {
        const sampleValues = ds.data?.slice(0, 10).map(row => row[header]).filter(val => val != null) || []
        const isNumeric = sampleValues.every(val => !isNaN(Number(val)) && val !== '')
        const isDate = sampleValues.some(val => !isNaN(Date.parse(val)))
        const uniqueValues = new Set(sampleValues).size

        return {
          name: header,
          type: isNumeric ? 'numeric' : isDate ? 'date' : 'text',
          uniqueValues: uniqueValues,
          sampleValues: sampleValues.slice(0, 3),
          hasNulls: sampleValues.length < 10
        }
      }) || []

      return {
        name: ds.name,
        columns: ds.headers,
        columnTypes: columnTypes,
        rowCount: ds.data?.length || 0,
        sampleData: ds.data?.slice(0, 3) || [],
        fileType: ds.fileType,
        // Dataset quality metrics
        qualityMetrics: {
          totalColumns: ds.headers?.length || 0,
          numericColumns: columnTypes.filter(col => col.type === 'numeric').length,
          textColumns: columnTypes.filter(col => col.type === 'text').length,
          dateColumns: columnTypes.filter(col => col.type === 'date').length
        }
      }
    })

    return {
      cells: cellsContext,
      datasets: datasetsContext,
      totalCells: cells.length,
      totalDatasets: datasets.length,
      selectedDatasets: selectedDatasets.map(ds => ds.name),
      workflowAnalysis: {
        patterns,
        workflow,
        currentStage: getCurrentWorkflowStage(workflow),
        suggestedNextSteps: workflow.suggestedNextSteps
      }
    }
  }, [cells, datasets, selectedDatasets, analyzeCellPatterns])

  const handleGenerate = async (quickPrompt?: string) => {
    const currentPrompt = quickPrompt || prompt.trim()

    if (!currentPrompt) {
      toast.error('Please enter a prompt')
      return
    }

    // Add user message to conversation
    const userMessage: ConversationMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: currentPrompt,
      timestamp: new Date()
    }
    setConversation(prev => [...prev, userMessage])

    // Abort any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    abortControllerRef.current = new AbortController()
    setIsStreaming(true)
    setStreamedResponse('')

    if (!quickPrompt) {
      setPrompt('') // Clear input only if not using quick action
    }

    try {
      const context = generateContext()

      const response = await fetch('/api/ai/copilot/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: currentPrompt,
          context: context,
          conversationHistory: conversation.slice(-5) // Last 5 messages for context
        }),
        signal: abortControllerRef.current.signal
      })

      if (!response.ok) {
        throw new Error('Failed to generate response')
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('No response body')
      }

      let accumulatedResponse = ''
      let accumulatedCode = ''
      let detectedLanguage = 'sql'
      const decoder = new TextDecoder()

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              if (data.content) {
                accumulatedResponse += data.content
                setStreamedResponse(accumulatedResponse)
              }
              if (data.code) {
                accumulatedCode += data.code
              }
              if (data.language) {
                detectedLanguage = data.language
              }
              if (data.done) {
                // Add assistant message to conversation
                const assistantMessage: ConversationMessage = {
                  id: Date.now().toString(),
                  type: 'assistant',
                  content: accumulatedResponse,
                  timestamp: new Date(),
                  code: accumulatedCode || undefined,
                  language: detectedLanguage
                }
                setConversation(prev => [...prev, assistantMessage])
                setStreamedResponse('')

                if (accumulatedCode) {
                  toast.success('Code generated! You can insert it into a cell.')
                }
                return
              }
            } catch (e) {
              // Ignore parsing errors for incomplete chunks
            }
          }
        }
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        toast.info('Generation cancelled')
      } else {
        console.error('Error generating response:', error)
        toast.error('Failed to generate response. Please try again.')
      }
    } finally {
      setIsStreaming(false)
      abortControllerRef.current = null
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleGenerate()
    }
    if (e.key === 'Escape') {
      setPrompt('')
    }
  }

  const handleStop = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    setIsStreaming(false)
    setStreamedResponse('')
    toast.info('Generation stopped')
  }

  const handleInsertCode = (code: string, language: string) => {
    if (selectedCellForInsertion) {
      onInsertCode(code, selectedCellForInsertion)
      toast.success('Code inserted into selected cell!')
    } else {
      onCreateNewCell(code, language)
      toast.success('New cell created with generated code!')
    }
  }

  // Generate cell summaries for context
  const cellSummaries: CellSummary[] = cells.map((cell, index) => ({
    id: cell.id,
    index: index + 1,
    language: cell.language,
    hasResult: !!cell.result?.data?.length,
    hasError: !!cell.error,
    summary: cell.content.slice(0, 100) + (cell.content.length > 100 ? '...' : ''),
    datasetCount: cell.selectedDatasetIds?.length || 0
  }))

  // Quick action prompts
  const quickActions = [
    { 
      label: "Analyze Data", 
      prompt: "Analyze the current datasets and provide insights", 
      icon: BarChart3,
      description: "Get statistical insights from your data"
    },
    { 
      label: "Create Chart", 
      prompt: "Create a visualization showing the main trends in the data", 
      icon: TrendingUp,
      description: "Generate code for data visualization"
    },
    { 
      label: "Data Quality", 
      prompt: "Check for missing values, duplicates, and data quality issues", 
      icon: Shield,
      description: "Validate and clean your data"
    },
    { 
      label: "Join Tables", 
      prompt: "Help me join multiple datasets together", 
      icon: Database,
      description: "Combine data from multiple sources"
    }
  ]

  if (isCollapsed) {
    return (
      <div className="w-12 h-full bg-background border-l border-border flex flex-col items-center py-4">
        <Button
          onClick={onToggleCollapse}
          variant="ghost"
          size="sm"
          className="w-8 h-8 p-0 mb-4"
        >
          <PanelRightOpen className="h-4 w-4" />
        </Button>
        <div className="flex flex-col gap-2">
          <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-violet-500 to-blue-500 flex items-center justify-center">
            <Brain className="h-4 w-4 text-white" />
          </div>
          {conversation.length > 0 && (
            <Badge variant="secondary" className="w-6 h-6 rounded-full p-0 flex items-center justify-center text-xs">
              {conversation.length}
            </Badge>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="w-80 h-full bg-background border-l border-border flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-violet-500 to-blue-500 flex items-center justify-center">
              <Brain className="h-4 w-4 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-sm">Data Copilot</h3>
              <p className="text-xs text-muted-foreground">AI-powered data assistant</p>
            </div>
          </div>
          <Button
            onClick={onToggleCollapse}
            variant="ghost"
            size="sm"
            className="w-8 h-8 p-0"
          >
            <PanelRightClose className="h-4 w-4" />
          </Button>
        </div>

        {/* Context Summary */}
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-xs">
            <Database className="h-3 w-3" />
            <span>{datasets.length} datasets</span>
            <Separator orientation="vertical" className="h-3" />
            <FileText className="h-3 w-3" />
            <span>{cells.length} cells</span>
          </div>
          {selectedDatasets.length > 0 && (
            <Badge variant="outline" className="text-xs">
              {selectedDatasets.length} selected dataset{selectedDatasets.length !== 1 ? 's' : ''}
            </Badge>
          )}
        </div>
      </div>

      {/* Tabbed Interface */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-3 mx-4 mt-2">
          <TabsTrigger value="chat" className="text-xs">
            <MessageSquare className="h-3 w-3 mr-1" />
            Chat
          </TabsTrigger>
          <TabsTrigger value="context" className="text-xs">
            <FileText className="h-3 w-3 mr-1" />
            Context
          </TabsTrigger>
          <TabsTrigger value="history" className="text-xs">
            <History className="h-3 w-3 mr-1" />
            History
          </TabsTrigger>
        </TabsList>

        <TabsContent value="chat" className="flex-1 flex flex-col mt-0">
          {/* Quick Actions */}
          <div className="p-4 border-b border-border">
            <h4 className="text-xs font-medium text-muted-foreground mb-2">Quick Actions</h4>
            <div className="grid grid-cols-2 gap-2">
              {quickActions.map((action, index) => {
                const IconComponent = action.icon
                return (
                  <Button
                    key={index}
                    onClick={() => handleGenerate(action.prompt)}
                    disabled={isStreaming}
                    variant="outline"
                    size="sm"
                    className="h-auto p-2 flex flex-col items-center gap-1 text-xs"
                    title={action.description}
                  >
                    <IconComponent className="h-3 w-3" />
                    <span className="text-xs">{action.label}</span>
                  </Button>
                )
              })}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="context" className="flex-1 flex flex-col mt-0">
          {/* Context Overview */}
          <div className="p-4">
            {/* Workflow Stage */}
            {(() => {
              const { workflow } = analyzeCellPatterns()
              const stage = getCurrentWorkflowStage(workflow)
              return (
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-xs">
                    <Target className="h-3 w-3" />
                    <span>Stage: {stage}</span>
                    <Badge variant="outline" className="text-xs">
                      {cells.filter(c => c.result?.data?.length).length}/{cells.length} executed
                    </Badge>
                  </div>

                  {/* Dataset Overview */}
                  <div className="space-y-2">
                    <h5 className="text-xs font-medium">Datasets ({datasets.length})</h5>
                    {datasets.slice(0, 3).map((dataset) => (
                      <div key={dataset.id} className="text-xs p-2 rounded bg-muted/50">
                        <div className="font-medium">{dataset.name}</div>
                        <div className="text-muted-foreground">
                          {dataset.headers?.length || 0} columns, {dataset.data?.length || 0} rows
                        </div>
                      </div>
                    ))}
                    {datasets.length > 3 && (
                      <div className="text-xs text-muted-foreground text-center">
                        +{datasets.length - 3} more datasets
                      </div>
                    )}
                  </div>

                  {/* Cell Summary */}
                  <div className="space-y-2">
                    <h5 className="text-xs font-medium">Cells ({cells.length})</h5>
                    {cellSummaries.slice(0, 4).map((cell) => (
                      <div key={cell.id} className="flex items-center gap-2 text-xs p-1 rounded bg-muted/50">
                        <Badge variant="outline" className="text-xs">
                          {cell.language}
                        </Badge>
                        <span className="flex-1 truncate">Cell {cell.index}</span>
                        {cell.hasResult && <Check className="h-3 w-3 text-green-500" />}
                        {cell.hasError && <X className="h-3 w-3 text-red-500" />}
                      </div>
                    ))}
                    {cellSummaries.length > 4 && (
                      <div className="text-xs text-muted-foreground text-center">
                        +{cellSummaries.length - 4} more cells
                      </div>
                    )}
                  </div>
                </div>
              )
            })()}
          </div>
        </TabsContent>

        <TabsContent value="history" className="flex-1 flex flex-col mt-0">
          {/* Conversation History */}
          <div className="p-4">
            <h4 className="text-xs font-medium text-muted-foreground mb-2">Recent Conversations</h4>
            {conversation.length === 0 ? (
              <div className="text-xs text-muted-foreground text-center py-4">
                No conversation history yet
              </div>
            ) : (
              <div className="space-y-2">
                {conversation.slice(-5).map((message) => (
                  <div key={message.id} className="text-xs p-2 rounded bg-muted/50">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant={message.type === 'user' ? 'default' : 'secondary'} className="text-xs">
                        {message.type}
                      </Badge>
                      <span className="text-muted-foreground">
                        {message.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                    <div className="truncate">{message.content}</div>
                    {message.code && (
                      <Badge variant="outline" className="text-xs mt-1">
                        {message.language} code
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
          {/* Conversation Area */}
          <div className="flex-1 flex flex-col">
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-4">
                {conversation.length === 0 && (
                  <div className="text-center py-8">
                    <Brain className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                    <p className="text-sm text-muted-foreground">
                      Ask me anything about your data!
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      I can analyze, visualize, and help you write code.
                    </p>
                  </div>
                )}
            
            {conversation.map((message) => (
              <div key={message.id} className={cn(
                "flex gap-2",
                message.type === 'user' ? 'justify-end' : 'justify-start'
              )}>
                <div className={cn(
                  "max-w-[80%] rounded-lg p-3 text-sm",
                  message.type === 'user'
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted'
                )}>
                  <p className="whitespace-pre-wrap">{message.content}</p>
                  {message.code && (
                    <div className="mt-3 space-y-2">
                      <div className="p-2 bg-background/50 rounded border text-xs font-mono">
                        <div className="flex items-center justify-between mb-2">
                          <Badge variant="outline" className="text-xs">
                            {message.language || 'code'}
                          </Badge>
                          <div className="flex gap-1">
                            <Button
                              onClick={() => navigator.clipboard.writeText(message.code!)}
                              size="sm"
                              variant="ghost"
                              className="h-6 w-6 p-0"
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                        <pre className="whitespace-pre-wrap text-xs">{message.code}</pre>
                      </div>

                      {/* Enhanced Code insertion options */}
                      <div className="space-y-2">
                        <div className="flex gap-2">
                          <Button
                            onClick={() => handleInsertCode(message.code!, message.language || 'sql')}
                            size="sm"
                            variant="outline"
                            className="h-7 px-2 text-xs"
                          >
                            <Plus className="h-3 w-3 mr-1" />
                            New Cell
                          </Button>

                          <Button
                            onClick={() => navigator.clipboard.writeText(message.code!)}
                            size="sm"
                            variant="ghost"
                            className="h-7 px-2 text-xs"
                          >
                            <Copy className="h-3 w-3 mr-1" />
                            Copy
                          </Button>
                        </div>

                        {cells.length > 0 && (
                          <div className="flex items-center gap-2">
                            <span className="text-xs text-muted-foreground">Insert into:</span>
                            <Select onValueChange={(cellId) => onInsertCode(message.code!, cellId)}>
                              <SelectTrigger className="h-7 w-32 text-xs">
                                <SelectValue placeholder="Select cell" />
                              </SelectTrigger>
                              <SelectContent>
                                {cells.map((cell, index) => (
                                  <SelectItem key={cell.id} value={cell.id}>
                                    <div className="flex items-center gap-2">
                                      <Badge variant="outline" className="text-xs">
                                        {cell.language}
                                      </Badge>
                                      <span>Cell {index + 1}</span>
                                      {cell.result?.data?.length && (
                                        <span className="text-xs text-muted-foreground">
                                          ({cell.result.data.length} rows)
                                        </span>
                                      )}
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Timestamp */}
                  <div className="mt-2 text-xs opacity-60">
                    {message.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              </div>
            ))}
            
            {isStreaming && streamedResponse && (
              <div className="flex gap-2 justify-start">
                <div className="max-w-[80%] rounded-lg p-3 text-sm bg-muted">
                  <div className="flex items-center gap-2 mb-2">
                    <Loader2 className="h-3 w-3 animate-spin" />
                    <span className="text-xs text-muted-foreground">Generating...</span>
                  </div>
                  <p className="whitespace-pre-wrap">{streamedResponse}</p>
                </div>
              </div>
            )}
            
            <div ref={conversationEndRef} />
          </div>
        </ScrollArea>

        {/* Input Area */}
        <div className="p-4 border-t border-border">
          <div className="flex gap-2">
            <Input
              ref={inputRef}
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="Ask about your data..."
              className="flex-1 text-sm"
              disabled={isStreaming}
            />
            {isStreaming ? (
              <Button
                onClick={handleStop}
                size="sm"
                variant="outline"
                className="px-3"
              >
                <X className="h-4 w-4" />
              </Button>
            ) : (
              <Button
                onClick={() => handleGenerate()}
                disabled={!prompt.trim()}
                size="sm"
                className="px-3"
              >
                <Send className="h-4 w-4" />
              </Button>
            )}
          </div>
        </ScrollArea>

        {/* Input Area */}
        <div className="p-4 border-t border-border">
          <div className="flex gap-2">
            <Input
              ref={inputRef}
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="Ask about your data..."
              className="flex-1 text-sm"
              disabled={isStreaming}
            />
            {isStreaming ? (
              <Button
                onClick={handleStop}
                size="sm"
                variant="outline"
                className="px-3"
              >
                <X className="h-4 w-4" />
              </Button>
            ) : (
              <Button
                onClick={() => handleGenerate()}
                disabled={!prompt.trim()}
                size="sm"
                className="px-3"
              >
                <Send className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
